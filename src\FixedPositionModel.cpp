#include "FixedPositionModel.h"
#include <QColor>
#include <QFont>
#include <QSet>
#include <algorithm>

FixedPositionModel::FixedPositionModel(QObject *parent)
    : QAbstractTableModel(parent), deltaTimeEnabled(false), baseTimestamp(0.0), batchProcessing(false)
{
    pendingLogs.reserve(BATCH_SIZE);
}

int FixedPositionModel::rowCount(const QModelIndex &) const
{
    return entries.size();
}

int FixedPositionModel::columnCount(const QModelIndex &) const
{
    return 8; // Timestamp, Channel, ID, Message Name, Direction, DLC, Data, Count
}

QVariant FixedPositionModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid() || index.row() >= entries.size())
        return QVariant();
    
    const FixedPositionEntry &entry = entries[index.row()];
    
    if (role == Qt::DisplayRole)
    {
        switch (index.column())
        {
        case 0: // Timestamp
            if (deltaTimeEnabled && baseTimestamp > 0.0)
                return QString::number(entry.lastTimestamp - baseTimestamp, 'f', 6);
            else
                return QString::number(entry.lastTimestamp, 'f', 6);
        case 1: // Channel
            return entry.channel;
        case 2: // ID
            return entry.messageId;
        case 3: // Message Name
            return entry.messageName.isEmpty() ? "Unknown" : entry.messageName;
        case 4: // Direction
            return entry.direction;
        case 5: // DLC
            return entry.dlc;
        case 6: // Data
        {
            QString dataStr;
            for (int i = 0; i < entry.lastData.size(); ++i)
            {
                if (i > 0) dataStr += " ";
                dataStr += QString("%1").arg(entry.lastData[i], 2, 16, QChar('0')).toUpper();
            }
            return dataStr;
        }
        case 7: // Update Count
            return entry.updateCount;
        }
    }
    else if (role == Qt::BackgroundRole)
    {
        // Highlight recently updated entries
        if (entry.updateCount > 1)
        {
            return QColor(255, 255, 200); // Light yellow for updated entries
        }
    }
    else if (role == Qt::FontRole)
    {
        if (entry.updateCount > 1)
        {
            QFont font;
            font.setBold(true);
            return font;
        }
    }
    
    return QVariant();
}

QVariant FixedPositionModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation == Qt::Horizontal && role == Qt::DisplayRole)
    {
        switch (section)
        {
        case 0: return deltaTimeEnabled ? "Delta Time" : "Timestamp";
        case 1: return "Channel";
        case 2: return "ID";
        case 3: return "Message Name";
        case 4: return "Direction";
        case 5: return "DLC";
        case 6: return "Data";
        case 7: return "Count";
        }
    }
    return QVariant();
}

void FixedPositionModel::setDeltaTimeEnabled(bool enabled)
{
    if (deltaTimeEnabled != enabled)
    {
        deltaTimeEnabled = enabled;
        emit headerDataChanged(Qt::Horizontal, 0, 0);
        emit dataChanged(index(0, 0), index(rowCount(QModelIndex()) - 1, 0));
    }
}

const FixedPositionEntry& FixedPositionModel::getEntry(int index) const
{
    return entries[index];
}

void FixedPositionModel::clear()
{
    beginResetModel();
    entries.clear();
    messageIdToRow.clear();
    baseTimestamp = 0.0;
    endResetModel();
}

int FixedPositionModel::getRowForMessageId(const QString &messageId) const
{
    return messageIdToRow.value(messageId, -1);
}

void FixedPositionModel::appendLogs(const QVector<CANLog> &batch)
{
    batchProcessing = true;
    pendingLogs.reserve(pendingLogs.size() + batch.size());
    
    for (const CANLog &log : batch)
    {
        pendingLogs.append(log);
        
        if (pendingLogs.size() >= BATCH_SIZE)
        {
            processBatch();
        }
    }
    
    // Process remaining logs
    if (!pendingLogs.isEmpty())
    {
        processBatch();
    }
    
    batchProcessing = false;
}

void FixedPositionModel::updateLog(const CANLog &log)
{
    if (batchProcessing)
    {
        pendingLogs.append(log);
        return;
    }
    
    if (baseTimestamp == 0.0)
    {
        baseTimestamp = log.timestamp;
    }
    
    int row = getRowForMessageId(log.id);
    
    if (row == -1)
    {
        addNewEntry(log);
    }
    else
    {
        updateExistingEntry(row, log);
    }
}

void FixedPositionModel::processBatch()
{
    if (pendingLogs.isEmpty())
        return;
        
    QVector<CANLog> newEntries;
    updatedRows.clear();
    
    // Process all pending logs
    for (const CANLog &log : pendingLogs)
    {
        if (baseTimestamp == 0.0)
        {
            baseTimestamp = log.timestamp;
        }
        
        int row = getRowForMessageId(log.id);
        
        if (row == -1)
        {
            // Collect new entries to add in batch
            newEntries.append(log);
        }
        else
        {
            // Update existing entry without emitting signals
            FixedPositionEntry &entry = entries[row];
            entry.lastTimestamp = log.timestamp;
            entry.channel = log.channel;
            entry.direction = log.direction;
            entry.dlc = log.dlc;
            entry.lastData = log.data;
            entry.updateCount++;
            
            updatedRows.insert(row);
        }
    }
    
    // Add new entries in batch
    if (!newEntries.isEmpty())
    {
        beginInsertRows(QModelIndex(), entries.size(), entries.size() + newEntries.size() - 1);
        
        for (const CANLog &log : newEntries)
        {
            FixedPositionEntry entry;
            entry.messageId = log.id;
            entry.messageName = log.messageName;
            entry.lastTimestamp = log.timestamp;
            entry.channel = log.channel;
            entry.direction = log.direction;
            entry.dlc = log.dlc;
            entry.lastData = log.data;
            entry.updateCount = 1;
            
            entries.append(entry);
            messageIdToRow[log.id] = entries.size() - 1;
        }
        
        endInsertRows();
    }
    
    // Emit dataChanged for updated rows in batch
    if (!updatedRows.isEmpty())
    {
        // Group consecutive rows for efficient updates
        QList<int> sortedRows = updatedRows.values();
        std::sort(sortedRows.begin(), sortedRows.end());
        
        int start = sortedRows.first();
        int end = start;
        
        for (int i = 1; i < sortedRows.size(); ++i)
        {
            if (sortedRows[i] == end + 1)
            {
                end = sortedRows[i];
            }
            else
            {
                // Emit for current range
                emit dataChanged(index(start, 0), index(end, columnCount(QModelIndex()) - 1));
                start = end = sortedRows[i];
            }
        }
        
        // Emit for last range
        emit dataChanged(index(start, 0), index(end, columnCount(QModelIndex()) - 1));
    }
    
    pendingLogs.clear();
}

void FixedPositionModel::addNewEntry(const CANLog &log)
{
    beginInsertRows(QModelIndex(), entries.size(), entries.size());
    
    FixedPositionEntry entry;
    entry.messageId = log.id;
    entry.messageName = log.messageName;
    entry.lastTimestamp = log.timestamp;
    entry.channel = log.channel;
    entry.direction = log.direction;
    entry.dlc = log.dlc;
    entry.lastData = log.data;
    entry.updateCount = 1;
    
    entries.append(entry);
    messageIdToRow[log.id] = entries.size() - 1;
    
    endInsertRows();
}

void FixedPositionModel::updateExistingEntry(int row, const CANLog &log)
{
    FixedPositionEntry &entry = entries[row];
    entry.lastTimestamp = log.timestamp;
    entry.channel = log.channel;
    entry.direction = log.direction;
    entry.dlc = log.dlc;
    entry.lastData = log.data;
    entry.updateCount++;
    
    // Update the entire row
    emit dataChanged(index(row, 0), index(row, columnCount(QModelIndex()) - 1));
}