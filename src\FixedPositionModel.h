#pragma once
#include <QAbstractTableModel>
#include <QVector>
#include <QMap>
#include <QSet>
#include "CANLogModel.h"

struct FixedPositionEntry
{
    QString messageId;
    QString messageName;
    double lastTimestamp;
    int channel;
    QString direction;
    int dlc;
    QVector<uint8_t> lastData;
    int updateCount;
};

class FixedPositionModel : public QAbstractTableModel
{
    Q_OBJECT
public:
    FixedPositionModel(QObject *parent = nullptr);
    int rowCount(const QModelIndex &) const override;
    int columnCount(const QModelIndex &) const override;
    QVariant data(const QModelIndex &, int role) const override;
    QVariant headerData(int section, Qt::Orientation orientation, int role) const override;
    
    void setDeltaTimeEnabled(bool enabled);
    bool isDeltaTimeEnabled() const { return deltaTimeEnabled; }
    
    const FixedPositionEntry& getEntry(int index) const;
    void clear();
    int getRowForMessageId(const QString &messageId) const;

public slots:
    void appendLogs(const QVector<CANLog> &batch);
    void updateLog(const CANLog &log);

private:
    void processBatch();
    void addNewEntry(const CANLog &log);
    void updateExistingEntry(int row, const CANLog &log);
    
    QVector<FixedPositionEntry> entries;
    QMap<QString, int> messageIdToRow; // Maps message ID to row index
    bool deltaTimeEnabled;
    double baseTimestamp;
    
    // Batch processing optimization
    QVector<CANLog> pendingLogs;
    QSet<int> updatedRows;
    bool batchProcessing;
    static const int BATCH_SIZE = 100;
};